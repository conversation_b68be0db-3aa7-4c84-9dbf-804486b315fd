//TABULA_GUID:{D8F83B6C-FC7B-4FFD-A212-50CFD1923121}
using System;
using System.Collections.Generic;
using System.Text;
using System.Net;
using System.IO;
using System.Threading.Tasks;
using System.Net.Http;
using System.Threading;

/**
 * HttpUtils:
 * Utilities for HTTP get/post
 */

/** Defines:
 * REFERENCE_OSUTILITIES:   will enable other functions using OSUtilities
 */

/* Version:
 *  1.1     
 *  1.0     start
 */

namespace Tabula
{
    internal class HttpUtils
    {
        // Static HttpClient instance to prevent file descriptor exhaustion
        // HttpClient is designed to be reused and is thread-safe
        private static HttpClient _httpClient;
        private static readonly object _httpClientLock = new object();

        // Property to access the static HttpClient instance with thread-safe initialization
        private static HttpClient HttpClientInstance
        {
            get
            {
                if (_httpClient == null)
                {
                    lock (_httpClientLock)
                    {
                        if (_httpClient == null)
                        {
                            var handler = new HttpClientHandler()
                            {
                                UseProxy = false,
                            };

                            var client = new HttpClient(handler);

                            // Add headers to prevent caching
                            client.DefaultRequestHeaders.CacheControl = new System.Net.Http.Headers.CacheControlHeaderValue
                            {
                                NoCache = true,
                                NoStore = true
                            };

                            _httpClient = client;
                        }
                    }
                }
                return _httpClient;
            }
        }

        public class HttpPostOptions
        {
            public bool ReceiveTextResponse = false;
            public bool ReceiveBinaryResponse = false;

            public string ReceivedText = "";
            public byte[] ReceivedBytes = new byte[0];
        }

        public static bool HttpGet(string uri)
        {
            string resp = "";
            return HttpGet(uri, ref resp);
        }

        public static bool HttpGet(string uri,ref string response)
        {
            string text;
            try
            {
                using (WebClient client = new WebClient())
                {
                    client.Proxy = null;
                    client.Encoding = Encoding.UTF8;
                    text = client.DownloadString(uri);
                    if (response != null)
                        response = text;
                }
            }
            catch { return false; }

            return true;
        }

        public static Task<bool> HttpGetAsync(string uri)
        {
            return Task<bool>.Run(() => HttpGet(uri));
        }


        public static bool HttpGetWithTimeout(string uri, TimeSpan timeout, ref string response)
        {
            try
            {
                WebRequest wreq = WebRequest.Create(uri);
                wreq.Timeout = (int) timeout.TotalMilliseconds;

                // This request will throw a WebException if it reaches the timeout limit before it is able to fetch the resource.
                using (WebResponse wresp = wreq.GetResponse())
                {
                    Console.WriteLine(((HttpWebResponse)wresp).StatusDescription);
                    using (StreamReader reader = new StreamReader(wresp.GetResponseStream()))
                    {
                        // Read the content into a STRING.
                        string responseFromServer = reader.ReadToEnd();

                        // Display the content.
                        response = responseFromServer;
                    }
                }

            }
            catch (Exception ex)
            {
               response = String.Format("EXCEPTION ({0}): {1}", ex.ToString(), ex.Message);
               return false;
            }

            return true;
        }


		[Obsolete]
		public static bool HttpPost(string uri, Dictionary<string, string> parameters = null)
        {
            HttpPostOptions options = new HttpPostOptions();
            return HttpPost(uri, parameters, ref options);
        }

		[Obsolete]
		public static bool HttpPost(string uri, Dictionary<string, string> parameters, ref HttpPostOptions options)
        {
			// create the full parameter string
			string parameter_string = "";
			if (parameters != null)
				foreach (KeyValuePair<string, string> kvp in parameters)
				{
					if (parameter_string != "")
						parameter_string += "&";
					parameter_string += kvp.Key + "=" + _escapeDataString(kvp.Value);
				}

            return HttpPost(uri, parameter_string, ref options);
		}

        [Obsolete]
		public static bool HttpPost(string uri, string parameter_string, ref HttpPostOptions options)
		{
            bool result = true;

            // parameters: name1=value1&name2=value2	
            WebRequest webRequest = WebRequest.Create(uri);
            webRequest.Proxy = null;
            webRequest.ContentType = "application/x-www-form-urlencoded";
            webRequest.Method = "POST";
            byte[] bytes = Encoding.ASCII.GetBytes(parameter_string);
            Stream os = null;
            try
            { // send the Post
                webRequest.ContentLength = bytes.Length;   //Count bytes to send

                using (os = webRequest.GetRequestStream())
                {
                    os.Write(bytes, 0, bytes.Length);         //Send it
                }
            }
            catch (WebException)
            {
                result = false;
            }


            if (os != null)
            {
                os.Close();
            }

            try
            {

                if (options.ReceiveTextResponse)
                {
                    WebResponse response = webRequest.GetResponse();

                    if (response != null)
                    {
                        StreamReader sr = new StreamReader(response.GetResponseStream());
                        options.ReceivedText = sr.ReadToEnd();
                        response.Close();
                    }
                }
                else if (options.ReceiveBinaryResponse)
                {
                    WebResponse response = webRequest.GetResponse();
                    byte[] buffer = new byte[4096*10];

                    if (response != null)
                    {
                        using (Stream responseStream = response.GetResponseStream())
                        {
                            using (MemoryStream memoryStream = new MemoryStream())
                            {
                                int count = 0;
                                do
                                {
                                    count = responseStream.Read(buffer, 0, buffer.Length);
                                    memoryStream.Write(buffer, 0, count);

                                } while (count != 0);

                                options.ReceivedBytes = memoryStream.ToArray();
                            }
                        }

                        response.Close();
                    }
                }
            }
            catch (Exception)
            {
                result = false;
            }

            return result;
        }

        // Version 2 based on HttpClient - FIXED VERSION
        // This version properly manages resources to prevent file descriptor exhaustion
        [Obsolete("Use HttpPost2Async for better resource management")]
		public static void HttpPost2(string uri, string parameterString, ref HttpPostOptions options)
		{
            // Use the async version but block on it for backward compatibility
            // This is not ideal but maintains the synchronous interface
            var task = HttpPost2Async(uri, parameterString, options, CancellationToken.None);
            task.Wait();

            // Copy results back to the ref parameter
            options.ReceivedText = task.Result.ReceivedText;
            options.ReceivedBytes = task.Result.ReceivedBytes;
		}

        // NEW: Proper async version that prevents resource exhaustion
        public static async Task<HttpPostOptions> HttpPost2Async(string uri, string parameterString, HttpPostOptions options, CancellationToken cancellationToken = default)
        {
            var content = new StringContent(parameterString, Encoding.UTF8, "application/x-www-form-urlencoded");

            try
            {
                // Use the static HttpClient instance to prevent resource exhaustion
                using (HttpResponseMessage response = await HttpClientInstance.PostAsync(uri, content, cancellationToken))
                {
                    response.EnsureSuccessStatusCode(); // Throws an exception if the HTTP response status is an error code.

                    if (options.ReceiveTextResponse)
                    {
                        options.ReceivedText = await response.Content.ReadAsStringAsync();
                    }
                    else if (options.ReceiveBinaryResponse)
                    {
                        options.ReceivedBytes = await response.Content.ReadAsByteArrayAsync();
                    }
                }
            }
            catch (HttpRequestException ex)
            {
                // Log the error for debugging
                Console.WriteLine($"HTTP Request error: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Console.WriteLine($"Inner Exception: {ex.InnerException.Message}");
                }
                throw; // Re-throw to maintain existing behavior
            }
            catch (TaskCanceledException ex) when (ex.InnerException is TimeoutException)
            {
                Console.WriteLine($"HTTP Request timeout: {ex.Message}");
                throw;
            }
            finally
            {
                // Dispose of the content to free resources
                content?.Dispose();
            }

            return options;
        }

        // NEW: Simple async POST method with JSON content
        public static async Task<string> PostJsonAsync(string uri, string jsonContent, CancellationToken cancellationToken = default)
        {
            var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

            try
            {
                using (HttpResponseMessage response = await HttpClientInstance.PostAsync(uri, content, cancellationToken))
                {
                    response.EnsureSuccessStatusCode();
                    return await response.Content.ReadAsStringAsync();
                }
            }
            finally
            {
                content?.Dispose();
            }
        }

        // NEW: Simple async GET method
        public static async Task<string> GetAsync(string uri, CancellationToken cancellationToken = default)
        {
            using (HttpResponseMessage response = await HttpClientInstance.GetAsync(uri, cancellationToken))
            {
                response.EnsureSuccessStatusCode();
                return await response.Content.ReadAsStringAsync();
            }
        }

        // NEW: Multipart form data POST method
        public static async Task<string> PostMultipartAsync(string uri, MultipartFormDataContent content, CancellationToken cancellationToken = default)
        {
            try
            {
                using (HttpResponseMessage response = await HttpClientInstance.PostAsync(uri, content, cancellationToken))
                {
                    response.EnsureSuccessStatusCode();
                    return await response.Content.ReadAsStringAsync();
                }
            }
            finally
            {
                // Note: content should be disposed by the caller since it may contain file streams
            }
        }

        // NEW: Cleanup method to dispose of the static HttpClient if needed
        public static void DisposeHttpClient()
        {
            lock (_httpClientLock)
            {
                if (_httpClient != null)
                {
                    _httpClient.Dispose();
                    _httpClient = null;
                }
            }
        }

		// Gets the file part of an URL
		public static string getUriFileName(string uri)
        {
            int iLastIndex = uri.LastIndexOf('/');
            return uri.Substring(iLastIndex + 1, (uri.Length - iLastIndex - 1));
        }

        // gets the url path without the file
        public static string getUriFolder(string uri)
        {
            int iLastIndex = uri.LastIndexOf('/');
            return uri.Substring(0, iLastIndex);
        }

        // downloads a file and writes it to desired folder (same name), throws any exception
        // if dest_file=="" will be guessed
        /*
        public static void downloadFileWithProgress(string url_dl, string dest_file, string dest_folder, Action<int> onProgress)
        {
            // the path to write the file to                
            if (dest_file == "")
                dest_file = getUriFileName(url_dl);

            // make sure folder exists
            if (dest_folder != "" && !Directory.Exists(dest_folder))
                Directory.CreateDirectory(dest_folder);

            string sFilePathToWriteFileTo = Path.Combine(dest_folder, dest_file);

            // first, we need to get the exact size (in bytes) of the file we are downloading
            Uri url = new Uri(url_dl);
            System.Net.HttpWebRequest request = (System.Net.HttpWebRequest)System.Net.WebRequest.Create(url);
            System.Net.HttpWebResponse response = (System.Net.HttpWebResponse)request.GetResponse();
            response.Close();
            // gets the size of the file in bytes
            Int64 iSize = response.ContentLength;

            if (iSize <= 0)
                throw new Exception("Cannot get file size though ContentLength header");

            // keeps track of the total bytes downloaded so we can update the progress bar
            Int64 iRunningByteTotal = 0;

            // use the webclient object to download the file
            using (System.Net.WebClient client = new System.Net.WebClient())
            {
                // open the file at the remote URL for reading
                using (System.IO.Stream streamRemote = client.OpenRead(new Uri(url_dl)))
                {
                    // using the FileStream object, we can write the downloaded bytes to the file system
                    using (Stream streamLocal = new FileStream(sFilePathToWriteFileTo, FileMode.Create, FileAccess.Write, FileShare.None))
                    {
                        // loop the stream and get the file into the byte buffer
                        int iByteSize = 0;
                        byte[] byteBuffer = new byte[iSize];
                        while ((iByteSize = streamRemote.Read(byteBuffer, 0, byteBuffer.Length)) > 0)
                        {
                            // write the bytes to the file system at the file path specified
                            streamLocal.Write(byteBuffer, 0, iByteSize);
                            iRunningByteTotal += iByteSize;

                            // calculate the progress out of a base "100"
                            double dIndex = (double)(iRunningByteTotal);
                            double dTotal = (double)byteBuffer.Length;
                            double dProgressPercentage = (dIndex / dTotal);
                            int iProgressPercentage = (int)(dProgressPercentage * 100);

                            // update the progress bar
                            if (onProgress!=null)
                                onProgress(iProgressPercentage);
                        }

                        // clean up the file stream
                        streamLocal.Close();
                    }

                    // close the connection to the remote server
                    streamRemote.Close();
                }
            }            
        }
        */

        // Has been taken from LauncherUtilities 
        public static bool downloadFileWithProgress(string url_file, string folder_dest, ref string downloaded_file_path, Action<int> onProgress)
        {
            // the path to write the file to                
            downloaded_file_path = folder_dest + @"\" + getUriFileName(url_file);

            // first, we need to get the exact size (in bytes) of the file we are downloading
            Uri url = new Uri(url_file);
            System.Net.HttpWebRequest request = (System.Net.HttpWebRequest)System.Net.WebRequest.Create(url);
            System.Net.HttpWebResponse response = (System.Net.HttpWebResponse)request.GetResponse();
            response.Close();
            // gets the size of the file in bytes
            Int64 iSize = response.ContentLength;

            // keeps track of the total bytes downloaded so we can update the progress bar
            Int64 iRunningByteTotal = 0;

            // use the webclient object to download the file
            using (System.Net.WebClient client = new System.Net.WebClient())
            {
                // open the file at the remote URL for reading
                using (System.IO.Stream streamRemote = client.OpenRead(new Uri(url_file)))
                {
                    // using the FileStream object, we can write the downloaded bytes to the file system
                    using (Stream streamLocal = new FileStream(downloaded_file_path, FileMode.Create, FileAccess.Write, FileShare.None))
                    {
                        // loop the stream and get the file into the byte buffer
                        int iByteSize = 0;
                        byte[] byteBuffer = new byte[iSize];
                        while ((iByteSize = streamRemote.Read(byteBuffer, 0, byteBuffer.Length)) > 0)
                        {
                            // write the bytes to the file system at the file path specified
                            streamLocal.Write(byteBuffer, 0, iByteSize);
                            iRunningByteTotal += iByteSize;

                            // calculate the progress out of a base "100"
                            double dIndex = (double)(iRunningByteTotal);
                            double dTotal = (double)byteBuffer.Length;
                            double dProgressPercentage = (dIndex / dTotal);
                            int iProgressPercentage = (int)(dProgressPercentage * 100);

                            // update the progress bar/text
                            if (onProgress != null)
                                onProgress(iProgressPercentage);
                        }

                        // clean up the file stream
                        streamLocal.Close();
                    }

                    // close the connection to the remote server
                    streamRemote.Close();
                }
            }            

            return true;
        }

#if REFERENCE_OSUTILITIES

        public static string downloadText(string uri)
        {
            string text = "";
            string downloadedfile = OSUtilities.GetTempFileName();

            using (WebClient client = new WebClient())
            {
                client.Proxy = null;
                client.DownloadFile(uri, downloadedfile);
            }

            // read the whole file
            StreamReader streamReader = new StreamReader(downloadedfile);
            text = streamReader.ReadToEnd();
            streamReader.Close();


            return text;
        }
#endif

        // thi will bypass the exception raised by Uri.EscapeDataString for strings that are too long
        private static string _escapeDataString(string uri)
        {
            int limit = 2000;

            StringBuilder sb = new StringBuilder();
            int loops = uri.Length / limit;

            for (int i = 0; i <= loops; i++)
            {
                if (i < loops)
                {
                    sb.Append(Uri.EscapeDataString(uri.Substring(limit * i, limit)));
                }
                else
                {
                    sb.Append(Uri.EscapeDataString(uri.Substring(limit * i)));
                }
            }

            return sb.ToString();
        }
    }
}
