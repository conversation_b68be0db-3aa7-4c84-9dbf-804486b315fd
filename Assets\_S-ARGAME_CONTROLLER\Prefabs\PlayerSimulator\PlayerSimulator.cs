using kcp2k;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Net.NetworkInformation;
using System.Threading.Tasks;
using Tabula.Licensing.LicenseActivator;
using Tabula.PMCore;
using Tabula.PMCore.Unity;
using Tabula.RPC;
using Tabula.WebServices.Arguments;
using UnityEngine;
using static Tabula.P2P.P2P;
using static Tabula.PWG.MobileController.Main;
using static Tabula.PWG.MobileController.MobileController;

namespace Tabula.PWG.MobileController
{

	public class PlayerSimulator : MonoBehaviour
	{
		public PMController_Client PMClient;

		// Simulated data
		[Header("Simulated Data")]
		public SimulatedData simData;               // provide the scriptable
		public float simDuration = 10;              // max simulation time
		public float simDelay = 0;
		public string simName = "";				// will be updated with status

		[Header("IDs")]
		public string	AUTH_CODE;
		public string	PLAYER_NAME;
		public int		PLAYER_ID;
		public string	PLAYER_GUID;

		// obtained throug P2P or forced
		public string	SERVER_ADDRESS;
		public int		SERVER_PORT;

		[Header("Parameters")]
		public bool  UseP2P = true;
		public float CheckAuthCodeTimeout = 60;
		public float P2PConnectionTimeout = 10;
		public float ClientConnectTimeout = 10;

		public float KeepAliveInterval = 0.5f;      // at least one message each half a second		
		public float SendDataFrequency = 30f;


		// Ping
		public float	PingInterval = 1.0f;   // send a ping to check latency and connection status
		public long		PingTime = 0;                   // last ping (ms)

		// Status / Keepalive
		public int		ServerTimeout = 5000;        // ms after which we timeout for lack of player status send from server

		public bool		IsConnected = false;

		[Header("Statistics")]
		public long		SentPackets = 0;
		public long		ReceivedPackets = 0;

		private float time_last_message = 0;
		private float time_last_ping = 0;       // for sending
		private System.Diagnostics.Stopwatch
					ping_timer = new System.Diagnostics.Stopwatch();      // for ping roundtrip calculation
		private System.Diagnostics.Stopwatch
					player_status_timer;                                // for player keepalive

		private PlayerControllerMessage DataMessage = new PlayerControllerMessage();    // the message to be sent

		private bool				ServerRequestedDisconnect = false;
		private ClientData			p2p_client_data;
		private PlayerJoinResult	PlayerJoinResult;                   // valid on a succesful connection
		private PlayerControllerServerMessage LastPlayerStatus;  // last received player status
		private bool				StopRequested = false;

		[ContextMenu("START")]
		public void StartSimulation()
		{
			StartCoroutine(StartSimulationCR());
		}

		[ContextMenu("STOP")]
		public void StopSimulation()
		{
			StopRequested = true;
		}

		public void SetNameData(string msg)
		{
			UnityMainThreadDispatcher.Instance().Enqueue(() =>
				gameObject.name = $"{simName} {msg}");
		}

		public IEnumerator StartSimulationCR(CoroutineWaitForTaskResult result = null)
		{
			gameObject.name = simName;

			yield return new WaitForSecondsRealtime(simDelay);

			PLAYER_GUID = System.Guid.NewGuid().ToString();

			if (UseP2P)
			{
				Debug.Log($"StartClientSimulation [P2P] AuthCode:{AUTH_CODE} Guid:{PLAYER_GUID}");
				SetNameData($"P2P: start");

				int check_authcode_result = -1;

				void _check_auth_code(string auth_code)
				{
					check_authcode_result = LicenseActivatorLib.CheckAuthCode(auth_code);
				}

				Debug.Log($"Checking AuthCode: {AUTH_CODE}");

				yield return UnityUtilities.WaitForTask(() => _check_auth_code(AUTH_CODE), result, CheckAuthCodeTimeout);

				Debug.Log($"Checking AuthCode result={check_authcode_result}");
				SetNameData($"P2P: auth={check_authcode_result}");

				if (check_authcode_result <= 0)
				{
					SetNameData($"P2P: authcode check failed!");
					Debug.Log($"Error: authcode check failed");
					yield break;
				}

				// Only go on if we receive a non admin authcode
				if (check_authcode_result != LicenseAuthCodeCheck_Response.Result_OK_NORMAL)
				{
					SetNameData($"P2P: authcode is admin");
					Debug.Log($"Error: cannot simulate test with admin authcode");
					yield break;
				}

				// setup P2P
				Debug.Log("P2P: start");

				var p2p_server_info = new P2P_ServerInfo();

				yield return ConnectP2P_CR(AUTH_CODE, p2p_server_info, P2PConnectionTimeout);

				SERVER_ADDRESS = p2p_server_info.ip;
				SERVER_PORT = p2p_server_info.port;

				if (!string.IsNullOrEmpty(p2p_server_info.ip))
				{
					SetNameData($"P2P: ready");
					Debug.Log($"P2P: ready ({SERVER_ADDRESS}:{SERVER_PORT}");
				}
				else
				{
					SetNameData($"P2P: failed");
					Debug.Log("P2P: failed");
					yield break;
				}
			}
			else
			{
				// direct connection ,server and port are valorized externally
				Debug.Log($"StartClientSimulation [direct] Guid:{PLAYER_GUID}");
			}

			
			player_status_timer = null;

			// async connection, wait for IsConnected
			var cr_result = new Tabula.CoroutineWaitForTaskResult();

			yield return PMClient.ConnectCR(SERVER_ADDRESS, SERVER_PORT, cr_result, ClientConnectTimeout);

			// Here ConnectedClientInfo.id is either a valid id or an error code (<0)

			if (cr_result.HasTimedOut)
			{
				SetNameData($"Connection timed out");
				Debug.Log("PMClient: OnConnectionTimedOut()");
				yield break;
			}
			else if (!PMClient.IsConnected)
			{
				SetNameData($"Connection failed");
				Debug.Log("PMClient: OnConnectionFailed()");
				yield break;
			}

			// Setup server -> client messages
			PMClient.Client.OnCustomMessageReceived = OnServerMessage;


			Exception join_exception = null;

			PlayerJoinResult = new Tabula.PMCore.PlayerJoinResult();

			yield return UnityUtilities.WaitForTask(() =>
			{
				try
				{
					// This is the real client login, where we ask if a player with that guid can enter
					PlayerJoinResult = PMClient.ClientRPC.NetworkPlayerJoin(PLAYER_GUID).Result;
				}
				catch (Exception ex)
				{
					SetNameData($"Join exception");
					join_exception = ex;
					PlayerJoinResult.player_client_id = -1;
				}
			});


			if (PlayerJoinResult.reason < 0 || join_exception != null)
			{
				// cannot join
				SetNameData($"Join error: reason={PlayerJoinResult.reason} exception={join_exception?.Message}");
				Debug.Log($"PMClient: cannot join, reason={PlayerJoinResult.reason} exception={join_exception?.Message}");
				yield break;
			}

			PLAYER_ID = PlayerJoinResult.player_client_id;

			Debug.Log($"PMClient: Join Succesful, reason={PlayerJoinResult.reason} player_id={PlayerJoinResult.player_client_id} player_flags={PlayerJoinResult.player_flags}");

			IsConnected = true;

			// start sending data looped, for a total amount of time
			if (simData!= null)
			{
				yield return SimulateInput(simData, simDuration);
			}

			//  disconnect only if it wasnt already disconnected
			if (IsConnected)
				yield return DisconnectCR();

			// cleanup
			ServerRequestedDisconnect = false;
			SentPackets = 0;
			ReceivedPackets = 0;
		}

		// sends a simulated inpt
		public IEnumerator SimulateInput(SimulatedData data, float max_time)
		{
			DataMessage.player_id = PLAYER_ID;
			DataMessage.flags = PlayerControllerMessage.Flags_GamePad;

			var end_time = Time.time + max_time;

			while (true)
			{
				if (Time.time > end_time)
					yield break;

				foreach (var d in data.items)
				{
					if (Time.time > end_time)
						yield break;

					if (StopRequested)
					{
						SetNameData($"StopRequested");
						yield break;
					}

					// buttons
					DataMessage.button_north = d.data.Buttons[0].pressed;
					DataMessage.button_south = d.data.Buttons[1].pressed;
					DataMessage.button_east = d.data.Buttons[2].pressed;
					DataMessage.button_west = d.data.Buttons[3].pressed;

					// stick
					DataMessage.stick1_x = d.data.Sticks[0].direction.x;
					DataMessage.stick1_y = d.data.Sticks[0].direction.y;

					DataMessage.SendToServer(PMClient.ClientKCP);
					time_last_message = Time.time;
					SentPackets++;

					// block until next item, but check for messages to send
					float next_item_time = Time.time + d.time;
					while (Time.time < next_item_time)
					{
						if (Time.time > end_time)
							yield break;

						// ping
						if ((Time.time - time_last_ping) > PingInterval)
						{
							SendPing();
						}

						// Server request a disconnect (es: player finished lives)
						if (ServerRequestedDisconnect)
						{
							Debug.Log("PMClient: Disconnecting as server requested.");

							yield return DisconnectCR(force: true);
							SetNameData($"ServerRequestedDisconnect");
							yield break;
						}

						// Check for player status from server timeout (undersponsive)
						if (player_status_timer != null && player_status_timer.ElapsedMilliseconds > ServerTimeout)
						{
							Debug.Log("PMClient: Player status timeout, server unresponsive, disconnecting.");

							yield return DisconnectCR(force: true);
							SetNameData($"Server unresponsive");
							yield break;
						}

						// limit send frequency
						if (SendDataFrequency != 0)
							if ((Time.time - time_last_message) > (1f / SendDataFrequency))
							{
								// resend last message
								DataMessage.SendToServer(PMClient.ClientKCP);
								time_last_message = Time.time;
								SentPackets++;
							}

						yield return null;
					}
				}
			}

		}

		bool _is_disconnecting = false;
		public IEnumerator DisconnectCR(bool force = false)
		{
			if (_is_disconnecting)
				yield break;

			_is_disconnecting = true;

			// Notiy server of player leave
			// NOTE: if we are disconnecting because killed (se we received a server request for disconnection) this player is already removed from game and the player leave will fail, that's OK
			PlayerLeftResult res = null;

			if (force)
			{
				// force it
				// TODO: should be a different reason
				res = new PlayerLeftResult(PlayerLeftResult.ReasonLeft_KeepAliveExpired);
			}
			else if (!ServerRequestedDisconnect)
			{
				yield return UnityUtilities.WaitForTask(() =>
				{
					res = PMClient.ClientRPC.NetworkPlayerLeave(PLAYER_GUID).Result;
					Debug.Log($"PMClient: NetworkPlayerLeave reason={res.reason}");
				});
			}
			else
			{
				// let's pretend it's all OK
				res = new PlayerLeftResult(PlayerLeftResult.ReasonLeft_ClientLeft);
			}


			Debug.Log($"PMClient: Player id={PLAYER_ID} left server, reason={res.reason}");

			// low-level client disconnect
			PMClient.Disconnect();

			PMClient.Client.OnCustomMessageReceived -= OnServerMessage;

			PLAYER_ID = -1;
			PLAYER_GUID = string.Empty;

			IsConnected = false;

			_is_disconnecting = false;

			switch (res.reason)
			{
				case PlayerLeftResult.ReasonLeft_ClientLeft:

					// user initiated leave
					SetNameData($"ReasonLeft_ClientLeft");

					break;

				case PlayerLeftResult.ReasonLeft_DenyClientNotFound:

					// this is OK if it was a server initiated leave (es: kill)
					SetNameData($"ReasonLeft_DenyClientNotFound");

					break;

				case PlayerLeftResult.ReasonLeft_ClientDisconnected:
					SetNameData($"ReasonLeft_ClientDisconnected");
					break;
			}


			Debug.Log("PMClient: " + (res.reason > 0 ? "OnLeaveSuccesful()" : " OnLeaveFailed())"));

			// NOTE: in Simulator also stop the p2p connection
			// Stopping this would create server unresponsive issues in other players
			// most likely because P2P.cs is now a singleton with static fields!
			/*
			if (UseP2P)
				StopP2PConnection();
			*/

			yield return null;
		}


		public void OnServerMessage(KcpChannel channel, Shared.KcpMessage msg)
		{
			ReceivedPackets++;

			switch (msg.header.code)
			{
				case PlayerControllerMessage.KCPMSG_PLAYERCONTROLLER_PING:
					break;

				case PlayerControllerMessage.KCPMSG_PLAYERCONTROLLER_COMMAND:

					// decode as json message
					var server_message = msg.DecodePayloadAsJson<PlayerControllerServerMessage>();

					if (server_message != null)
					{
						switch (server_message.command)
						{
							// Keepalive and status, 
							case PlayerControllerServerMessage.SERVER_PLAYER_STATUS:

								PLAYER_NAME = server_message.player_name;

								// debug only if something has changed
								if (LastPlayerStatus == null || !LastPlayerStatus.Equals(server_message))
								{
									Debug.Log($"PMClient: OnServerMessage({server_message.command}) player_name={server_message.player_name} player_status={server_message.player_status} wait_queue={server_message.wait_queue} lives_health={server_message.player_lives}/{server_message.player_health}");

									// update sim name with status for better tracking
									string status = "";
									switch (server_message.player_status)
									{
										case 0: status = "waiting";break;
										case 1: status = "setup"; break;
										case 2: status = "ready"; break;
										case 3: status = "playing"; break;

										default:
											status = server_message.player_status.ToString(); break;
									}

									SetNameData($"({server_message.player_name}) {status} {server_message.player_lives}/{server_message.player_health} queue={server_message.wait_queue}");
								}

								if (player_status_timer == null)
								{
									player_status_timer = new System.Diagnostics.Stopwatch();
									player_status_timer.Start();
								}
								else
									player_status_timer.Restart();

								// TODO: other notifications

								break;


							// Lost 1 life
							case PlayerControllerServerMessage.SERVER_REPORT_KILL:
								Debug.Log($"PMClient: OnServerMessage({server_message.command})");
								SetNameData($"({server_message.player_name}) SERVER_REPORT_KILL");
								break;


							// Lost all lives, need to disconnect
							case PlayerControllerServerMessage.SERVER_ASK_DISCONNECT:
								Debug.Log($"PMClient: OnServerMessage({server_message.command})");
								SetNameData($"({server_message.player_name}) SERVER_ASK_DISCONNECT");
								ServerRequestedDisconnect = true;
								break;

						}

						LastPlayerStatus = server_message;
					}
					else
						Debug.LogError("PMClient: malformed server message");

					break;
			}
		}

		public void SendPing()
		{
			// bare message with no payload

			var m = new Shared.KcpMessage()
			{
				header = new Shared.KcpMessage._header()
				{
					code = PlayerControllerMessage.KCPMSG_PLAYERCONTROLLER_PING,
					chunk_sequence = 1,
					chunk_count = 1
				}
			};

			lock (PMClient.ClientKCP)
			{
				PMClient.ClientKCP?.Send(new ArraySegment<byte>(m.ToByteArray()), KcpChannel.Unreliable);
			}

			time_last_ping = Time.time;
			ping_timer.Restart();
		}


		public IEnumerator ConnectP2P_CR(string auth_code, P2P_ServerInfo p2p_server_info, float max_time = -1)
		{
			p2p_client_data = null;
			bool client_result = false;

			Task.Run(() => StartClient(auth_code,
				async (client_data) =>
				{
					p2p_client_data = client_data;
					client_result = true;
				}));

			if (max_time == -1)
				max_time = float.MaxValue;

			var t_start = Time.time;
			while (!client_result && p2p_client_data == null && Time.time < (t_start + max_time))
				yield return null;

			if (p2p_client_data == null)
			{
				p2p_server_info.ip = null;
				yield break;
			}

			p2p_server_info.connection_state = p2p_client_data.status;

			if (p2p_client_data.status == ClientData.State.Connected)
			{
				p2p_server_info.ip = p2p_client_data.ip;
				p2p_server_info.port = p2p_client_data.port;
			}
			else
			{
				p2p_server_info.ip = null;
			}

		}

		public void StopP2PConnection()
		{
			Task.Run(() => P2P.P2P.StopClient(p2p_client_data.matchMakerInstance));
		}

	}
}
