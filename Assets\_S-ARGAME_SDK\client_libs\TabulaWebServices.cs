using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.IO;
using System.Net.Http;
using Tabula.WebServices.Arguments;

// PMController version, stripped down

namespace Tabula.WebServices
{
	internal static class Constants
	{
		public const string password = "jasdj38JJSAl3HHkh3484f52kJNj8h48mrDsc2945";
	}

	public static class URL
	{
		public enum Modes
		{
			Debug = 1,
			Production =2
		}

		public static Modes Mode = Modes.Production;

		// webservices.tabulatouch.com are C# Azure Functions
		private const string ProductionBaseUrl1 = "https://webservices.tabulatouch.com/api/";
		private const string LocalBaseUrl1 = "http://localhost:7148/api/";

		private static string _base1 => (Mode == Modes.Production ? ProductionBaseUrl1 : LocalBaseUrl1);

		private const string _LicenseAuthCodeCheck = "LicenseAuthCodeCheck?code=EDCKLZUBy5p2Ty6k4PvDUXp9sodQg49Xwmf3eKUsFStKAzFu5wNckA==";
		public static string LicenseAuthCodeCheck => _base1 + _LicenseAuthCodeCheck;

		private const string _CalibrationImageUpload = "CalibrationImageUpload?code=eIXA0tgDVcAnoc08PZRF-SXyl-lgbWdHzhhq_7ZAldbRAzFutAoI1w==";
		public static string CalibrationImageUpload => _base1 + _CalibrationImageUpload;
	}

#if TABULAWEBSERVICES_CLIENT

	#region Client

	// Client class for calls
	public static class Client
	{
		public static Action<string> OnLog;
		public static Action<Exception> OnException;

		public static Resp POST<Resp>(string url, object obj, bool encrypt = true)
		{
			return POST<Resp>(url, JsonConvert.SerializeObject(obj), encrypt);
		}

		public static Resp POST<Resp>(string url, string body, bool encrypt = true)
		{
			HttpUtils.HttpPostOptions opts = new HttpUtils.HttpPostOptions();
			opts.ReceiveTextResponse = true;

			try
			{

				// if (HttpUtils.HttpPost(url, encrypt ? Serializer.EncryptString(body, Constants.password) : body, ref opts))
				HttpUtils.HttpPost2(url, encrypt ? Serializer.EncryptString(body, Constants.password) : body, ref opts);

				{				
					// supports both an OkObjectResult (Azure C#) or a plan text (Azure Python or standard)
					string value_received = "";

					try
					{
						OkObjectResult result = JsonConvert.DeserializeObject<OkObjectResult>(opts.ReceivedText);

						if (result == null || result.StatusCode != 200)
						{
							// If it's an error it's not encrypted
							throw new Exception($"Error in call, status={result.StatusCode}, message={result.Value}");
						}

						if (!string.IsNullOrEmpty((string) result.Value))
							value_received = (string) result.Value;
						else
							value_received = opts.ReceivedText;
					}
					catch
					{
						value_received = opts.ReceivedText;
					}
					

					var value_decrypted = encrypt ? Serializer.DecryptString(value_received, Constants.password) : value_received;
					var response = JsonConvert.DeserializeObject<Resp>(value_decrypted);

					OnLog?.Invoke(JsonConvert.SerializeObject(response, Formatting.Indented));

					return response;
				}				
			}
			catch (Exception ex)
			{
				OnException?.Invoke(ex);
			}

			return default;
		}

		public static LicenseAuthCodeCheck_Response LicenseAuthCodeCheck(string product, string auth_code)
		{
			var req = new LicenseAuthCodeCheck_Request();

			req.product = product;
			req.auth_code = auth_code;

			var resp = POST<LicenseAuthCodeCheck_Response>(URL.LicenseAuthCodeCheck, req);

			return resp;
		}

		public static CalibrationImageUpload_Response CalibrationImageUpload(string product, string auth_code, string image_path)
		{
			var req = new CalibrationImageUpload_Request()
			{
				product = product,
				auth_code = auth_code
			};

			try
			{

				//NOTE: Unlike POST() call we are using HttpClient directly

				HttpClient httpClient = new HttpClient();

				// Prepare the content of the form
				MultipartFormDataContent form = new MultipartFormDataContent();

				var req_str = Serializer.EncryptString(JsonConvert.SerializeObject(req), Constants.password);

				// Add the "request" parameter to the form
				form.Add(new StringContent(req_str), "request");

				// Read the image file and add it to the form
				byte[] imageBytes = File.ReadAllBytes(image_path);
				form.Add(new ByteArrayContent(imageBytes, 0, imageBytes.Length), "image", "image.jpg");

				// Post the form to a URL
				HttpResponseMessage response = httpClient.PostAsync(URL.CalibrationImageUpload, form).Result;

				// Check the result
				OkObjectResult result = null;
				if (response != null && response.IsSuccessStatusCode)
				{
					result = JsonConvert.DeserializeObject<OkObjectResult>(response.Content.ReadAsStringAsync().Result);
				}
				else
				{
					// If it's an error it's not encrypted
					throw new Exception($"Error in CalibrationImageUpload call, status={response.StatusCode}");
				}

				var value_decrypted = Serializer.DecryptString(result.Value as string, Constants.password);
				var value_obj = JsonConvert.DeserializeObject<CalibrationImageUpload_Response>(value_decrypted);

				OnLog?.Invoke(JsonConvert.SerializeObject(value_obj, Formatting.Indented));

				return value_obj;
			}
			catch(Exception ex)
			{
				OnException?.Invoke(ex);
				return null;
			}
		}
	}

	#endregion

#endif
}


namespace Tabula.WebServices.Arguments
{
	#region LicenseAuthCodeCheck

	[Serializable]
	public class LicenseAuthCodeCheck_Request
	{
		public string product;
		public string auth_code;
		public bool admin;
	}

	[Serializable]
	public class LicenseAuthCodeCheck_Response
	{
		public int result;

		public const int Result_OK_NORMAL = 1;  // normal auth_code
		public const int Result_OK_ADMIN = 2;   // admin auth_code

		public const int Result_ERROR = -1;
	}

	#endregion


	#region CalibrationImageUpload

	// this will be put into a "data" parameter in the multiform upload
	[Serializable]
	public class CalibrationImageUpload_Request
	{
		public string product;
		public string auth_code;
	}

	[Serializable]
	public class CalibrationImageUpload_Response
	{
		public int result;

		public const int Result_ERROR = 0;  // generic error
		public const int Result_OK = 1;	// the upload went through
	}

	#endregion
}
