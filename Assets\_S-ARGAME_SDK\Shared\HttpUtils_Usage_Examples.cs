//TABULA_GUID:{EXAMPLE-HTTPUTILS-USAGE}
using System;
using System.Threading;
using System.Threading.Tasks;
using Tabula;

/**
 * HttpUtils Usage Examples:
 * This file demonstrates how to properly use the fixed HttpUtils methods
 * to prevent file descriptor exhaustion and resource leaks.
 */

namespace Tabula.Examples
{
    public class HttpUtilsUsageExamples
    {
        // Example 1: Using the new async POST method (RECOMMENDED)
        public async Task ExampleAsyncPost()
        {
            try
            {
                var options = new HttpUtils.HttpPostOptions
                {
                    ReceiveTextResponse = true
                };

                // This method properly manages resources and prevents file descriptor exhaustion
                var result = await HttpUtils.HttpPost2Async("https://api.example.com/data", 
                    "param1=value1&param2=value2", options);

                Console.WriteLine($"Response: {result.ReceivedText}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        // Example 2: Using the new JSON POST method
        public async Task ExampleJsonPost()
        {
            try
            {
                string jsonData = "{\"key\":\"value\",\"number\":123}";
                string response = await HttpUtils.PostJsonAsync("https://api.example.com/json", jsonData);
                Console.WriteLine($"JSON Response: {response}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        // Example 3: Using the new async GET method
        public async Task ExampleAsyncGet()
        {
            try
            {
                string response = await HttpUtils.GetAsync("https://api.example.com/data");
                Console.WriteLine($"GET Response: {response}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        // Example 4: Making many requests without resource exhaustion
        public async Task ExampleManyRequests()
        {
            try
            {
                for (int i = 0; i < 1000; i++)
                {
                    var options = new HttpUtils.HttpPostOptions
                    {
                        ReceiveTextResponse = true
                    };

                    // This will NOT cause file descriptor exhaustion because it reuses the static HttpClient
                    var result = await HttpUtils.HttpPost2Async("https://api.example.com/data", 
                        $"request_id={i}", options);

                    Console.WriteLine($"Request {i}: {result.ReceivedText}");

                    // Optional: Add a small delay if needed
                    // await Task.Delay(10);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        // Example 5: Using cancellation tokens for timeout control
        public async Task ExampleWithTimeout()
        {
            try
            {
                using (var cts = new CancellationTokenSource(TimeSpan.FromSeconds(30)))
                {
                    string response = await HttpUtils.GetAsync("https://api.example.com/slow-endpoint", cts.Token);
                    Console.WriteLine($"Response: {response}");
                }
            }
            catch (TaskCanceledException)
            {
                Console.WriteLine("Request timed out after 30 seconds");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        // Example 6: Backward compatibility with the old synchronous method (NOT RECOMMENDED)
        public void ExampleBackwardCompatibility()
        {
            try
            {
                var options = new HttpUtils.HttpPostOptions
                {
                    ReceiveTextResponse = true
                };

                // This still works but is marked as obsolete
                // It internally uses the async version but blocks on it
                HttpUtils.HttpPost2("https://api.example.com/data", "param1=value1", ref options);

                Console.WriteLine($"Response: {options.ReceivedText}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        // Example 7: Proper cleanup when shutting down the application
        public void ExampleCleanup()
        {
            // Call this when your application is shutting down to properly dispose resources
            HttpUtils.DisposeHttpClient();
        }
    }

    // Unity MonoBehaviour example for async operations
    #if UNITY_2017_1_OR_NEWER
    using UnityEngine;

    public class HttpUtilsUnityExample : MonoBehaviour
    {
        async void Start()
        {
            try
            {
                // Example of using async methods in Unity
                string response = await HttpUtils.GetAsync("https://api.example.com/unity-data");
                Debug.Log($"Unity Response: {response}");
            }
            catch (Exception ex)
            {
                Debug.LogError($"Unity HTTP Error: {ex.Message}");
            }
        }

        void OnApplicationQuit()
        {
            // Clean up resources when Unity application quits
            HttpUtils.DisposeHttpClient();
        }
    }
    #endif
}
