# HttpUtils File Descriptor Exhaustion Fix

## Problem Description

The original `HttpPost2` method in `HttpUtils.cs` was causing file descriptor exhaustion with the error:
```
System.NotSupportedException: Could not register to wait for file descriptor XXXXXX
```

This occurred when making many HTTP requests because:

1. **New HttpClient instances**: Each call created a new `HttpClient` and `HttpClientHandler`
2. **Resource leaks**: `HttpResponseMessage` objects were not properly disposed
3. **Blocking async calls**: Using `.Result` on async operations can cause deadlocks and resource issues
4. **Socket exhaustion**: Connections were not being reused or properly closed

## Root Cause Analysis

The error happens when the operating system runs out of available file descriptors (which include network sockets). Each HTTP request potentially opens a new socket, and if these aren't properly managed, the system hits its limit.

## Solution Implemented

### 1. Static HttpClient Instance
- Added a static, thread-safe `HttpClient` instance that's reused across all requests
- Uses `Lazy<HttpClient>` for thread-safe initialization
- Configured with proper settings (no proxy, cache control headers)

### 2. Proper Resource Management
- All `HttpResponseMessage` objects are now wrapped in `using` statements
- `StringContent` objects are properly disposed in `finally` blocks
- Response content is fully read to completion

### 3. Async-First Design
- New `HttpPost2Async` method provides proper async implementation
- Old `HttpPost2` method maintained for backward compatibility but marked as obsolete
- Added cancellation token support for timeout control

### 4. Additional Utility Methods
- `PostJsonAsync`: Simple JSON POST requests
- `GetAsync`: Simple GET requests
- `DisposeHttpClient`: Cleanup method for application shutdown

## Key Changes Made

### Before (Problematic Code):
```csharp
public static void HttpPost2(string uri, string parameterString, ref HttpPostOptions options)
{
    var handler = new HttpClientHandler() { UseProxy = false };
    using (var client = new HttpClient(handler))
    {
        var content = new StringContent(parameterString, Encoding.UTF8, "application/x-www-form-urlencoded");
        HttpResponseMessage response = client.PostAsync(uri, content).Result;  // PROBLEM: .Result
        response.EnsureSuccessStatusCode(); // PROBLEM: No using statement
        
        if (options.ReceiveTextResponse)
        {
            options.ReceivedText = response.Content.ReadAsStringAsync().Result; // PROBLEM: .Result
        }
    } // PROBLEM: New HttpClient every call
}
```

### After (Fixed Code):
```csharp
// Static HttpClient instance (reused)
private static readonly Lazy<HttpClient> _httpClient = new Lazy<HttpClient>(() => {
    var handler = new HttpClientHandler() { UseProxy = false };
    var client = new HttpClient(handler);
    client.DefaultRequestHeaders.CacheControl = new CacheControlHeaderValue { NoCache = true, NoStore = true };
    return client;
});

public static async Task<HttpPostOptions> HttpPost2Async(string uri, string parameterString, HttpPostOptions options, CancellationToken cancellationToken = default)
{
    var content = new StringContent(parameterString, Encoding.UTF8, "application/x-www-form-urlencoded");
    
    try
    {
        using (HttpResponseMessage response = await HttpClientInstance.PostAsync(uri, content, cancellationToken))
        {
            response.EnsureSuccessStatusCode();
            
            if (options.ReceiveTextResponse)
            {
                options.ReceivedText = await response.Content.ReadAsStringAsync();
            }
            else if (options.ReceiveBinaryResponse)
            {
                options.ReceivedBytes = await response.Content.ReadAsByteArrayAsync();
            }
        }
    }
    finally
    {
        content?.Dispose();
    }
    
    return options;
}
```

## Migration Guide

### For New Code (Recommended):
```csharp
// Use the new async methods
var options = new HttpUtils.HttpPostOptions { ReceiveTextResponse = true };
var result = await HttpUtils.HttpPost2Async(url, parameters, options);

// Or for JSON
string response = await HttpUtils.PostJsonAsync(url, jsonData);
```

### For Existing Code:
The old `HttpPost2` method still works but is marked as obsolete. It internally uses the new async implementation but blocks on it for backward compatibility.

## Performance Benefits

1. **Resource Efficiency**: Reuses connections through HTTP Keep-Alive
2. **Reduced Overhead**: No need to establish new connections for each request
3. **Better Scalability**: Can handle many more concurrent requests
4. **Proper Cleanup**: No resource leaks or file descriptor exhaustion

## Testing Recommendations

1. Test making 1000+ requests in a loop to verify no resource exhaustion
2. Monitor file descriptor usage during high-load scenarios
3. Test cancellation token functionality for timeout scenarios
4. Verify backward compatibility with existing code

## Important Notes

- The static `HttpClient` instance is thread-safe and designed for reuse
- Always use the async methods (`HttpPost2Async`, `PostJsonAsync`, `GetAsync`) for new code
- Call `HttpUtils.DisposeHttpClient()` when your application shuts down
- The fix maintains full backward compatibility with existing code
